using UnityEngine;
using UnityEngine.UI;

[RequireComponent(typeof(Image))]
public class Item : MonoBehaviour
{
    public string Name;
    public Vector2 Size = Vector2.one;

    public Item(string Name, Vector2 Size)
    {
        this.Name = Name;
        this.Size = Size;
    }

    private void Awake()
    {
        UpdateSize();
    }

    private void UpdateSize()
    {
        RectTransform rectTransform = this.transform as RectTransform;
        float width = InventoryStats.CELL_SIZE * Size.x + (InventoryStats.CELL_GAP * (Size.x - 1));
        float height = InventoryStats.CELL_SIZE * Size.y + (InventoryStats.CELL_GAP * (Size.y - 1));
        rectTransform.sizeDelta = new Vector2(width, height);
    }

    public class Builder
    {
        public string Name;
        public Vector2 Size = Vector2.one;

        public Builder(string name, Vector2 size) //Sprite icon, 
        {
            Name = name;
            //Icon = icon;
            Size = size;
        }

        public Item Build()
        {
            GameObject itemObject = new GameObject("Item_" + Name, typeof(RectTransform));
            Item item = itemObject.AddComponent<Item>();
            item.Name = Name;
            //item.Icon = Icon;
            item.Size = Size;

            item.UpdateSize();

            return item;
        }
    }
}