using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.InputSystem;
public class DragItemHandler : MonoBehaviour
{
    private bool isDragging = false;
    private Vector2 mouseStartPosition;
    private Vector2 dragItemStartPosition;
    private RectTransform draggedItem;

    private void OnEnable()
    {
        MouseInputHandler.OnMouseClickDown += Clicked;
        MouseInputHandler.OnMouseClickUp += Released;
        MouseInputHandler.OnMouseMoved += Drag;
    }

    private void OnDisable()
    {
        MouseInputHandler.OnMouseClickDown -= Clicked;
        MouseInputHandler.OnMouseClickUp -= Released;
        MouseInputHandler.OnMouseMoved -= Drag;
    }

    private void Clicked(GameObject hitObject)
    {
        if (hitObject == null) return;
        if (hitObject.TryGetComponent<Item>(out Item dragItem))
        {
            isDragging = true;
            draggedItem = dragItem.transform as RectTransform;
            mouseStartPosition = Mouse.current.position.ReadValue();
            dragItemStartPosition = draggedItem.position;
        }
    }
    private void Released(GameObject hitObject)
    {
        if (draggedItem == null) return;

        List<InventorySlot> slots = FindObjectsByType<InventorySlot>(FindObjectsSortMode.None).ToList();
        //List<InventoryWhiteboard> whiteboards = FindObjectsByType<InventoryWhiteboard>(FindObjectsSortMode.None).ToList();

        // Find all slots that intersect with the dragged item
        List<InventorySlot> intersectingSlots = new List<InventorySlot>();
        foreach (InventorySlot slot in slots)
        {
            RectTransform slotRect = slot.transform as RectTransform;
            if (DoRectTransformsIntersect(draggedItem, slotRect))
            {
                intersectingSlots.Add(slot);
            }
        }

        // Debug output to show which slots are intersecting
        Debug.Log($"Dragged item intersects with {intersectingSlots.Count} slots:");
        foreach (InventorySlot slot in intersectingSlots)
        {
            Debug.Log($"  - Slot at ({slot.X}, {slot.Y})");
        }

        isDragging = false;
        draggedItem = null;
        mouseStartPosition = Vector2.zero;
        dragItemStartPosition = Vector2.zero;
    }
    private void Drag(Vector2 position)
    {
        if (!isDragging) return;
        Vector2 mousePosition = Mouse.current.position.ReadValue();
        draggedItem.position = dragItemStartPosition + (mousePosition - mouseStartPosition);
    }

    /// <summary>
    /// Checks if two RectTransforms intersect in any way (overlapping, touching, one inside the other)
    /// </summary>
    /// <param name="rect1">First RectTransform</param>
    /// <param name="rect2">Second RectTransform</param>
    /// <returns>True if the RectTransforms intersect, false otherwise</returns>
    private bool DoRectTransformsIntersect(RectTransform rect1, RectTransform rect2)
    {
        if (rect1 == null || rect2 == null) return false;

        // Get world space corners for both RectTransforms
        Vector3[] corners1 = new Vector3[4];
        Vector3[] corners2 = new Vector3[4];

        rect1.GetWorldCorners(corners1);
        rect2.GetWorldCorners(corners2);

        // Convert to screen space bounds for easier calculation
        // corners are in order: bottom-left, top-left, top-right, bottom-right
        float rect1MinX = corners1[0].x;
        float rect1MaxX = corners1[2].x;
        float rect1MinY = corners1[0].y;
        float rect1MaxY = corners1[1].y;

        float rect2MinX = corners2[0].x;
        float rect2MaxX = corners2[2].x;
        float rect2MinY = corners2[0].y;
        float rect2MaxY = corners2[1].y;

        // Check for intersection using AABB (Axis-Aligned Bounding Box) collision detection
        // Two rectangles intersect if they overlap on both X and Y axes
        bool xOverlap = rect1MinX <= rect2MaxX && rect1MaxX >= rect2MinX;
        bool yOverlap = rect1MinY <= rect2MaxY && rect1MaxY >= rect2MinY;

        return xOverlap && yOverlap;
    }

}