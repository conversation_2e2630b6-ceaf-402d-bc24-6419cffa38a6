using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.InputSystem;
public class DragItemHandler : MonoBehaviour
{
    private bool isDragging = false;
    private Vector2 mouseStartPosition;
    private Vector2 dragItemStartPosition;
    private RectTransform draggedItem;

    private void OnEnable()
    {
        MouseInputHandler.OnMouseClickDown += Clicked;
        MouseInputHandler.OnMouseClickUp += Released;
        MouseInputHandler.OnMouseMoved += Drag;
    }

    private void OnDisable()
    {
        MouseInputHandler.OnMouseClickDown -= Clicked;
        MouseInputHandler.OnMouseClickUp -= Released;
        MouseInputHandler.OnMouseMoved -= Drag;
    }

    private void Clicked(GameObject hitObject)
    {
        if (hitObject == null) return;
        if (hitObject.TryGetComponent<Item>(out Item dragItem))
        {
            isDragging = true;
            draggedItem = dragItem.transform as RectTransform;
            mouseStartPosition = Mouse.current.position.ReadValue();
            dragItemStartPosition = draggedItem.position;
        }
    }
    private void Released(GameObject hitObject)
    {
        List<InventorySlot> slots = FindObjectsByType<InventorySlot>(FindObjectsSortMode.None).ToList();
        List<InventoryWhiteboard> whiteboards = FindObjectsByType<InventoryWhiteboard>(FindObjectsSortMode.None).ToList();



        isDragging = false;
        draggedItem = null;
        mouseStartPosition = Vector2.zero;
        dragItemStartPosition = Vector2.zero;
    }
    private void Drag(Vector2 position)
    {
        if (!isDragging) return;
        Vector2 mousePosition = Mouse.current.position.ReadValue();
        draggedItem.position = dragItemStartPosition + (mousePosition - mouseStartPosition);
    }

}