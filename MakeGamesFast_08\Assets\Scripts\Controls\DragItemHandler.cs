using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.InputSystem;
public class DragItemHandler : MonoBehaviour
{
    private bool isDragging = false;
    private Vector2 mouseStartPosition;
    private Vector2 dragItemStartPosition;
    private RectTransform draggedItem;

    private void OnEnable()
    {
        MouseInputHandler.OnMouseClickDown += Clicked;
        MouseInputHandler.OnMouseClickUp += Released;
        MouseInputHandler.OnMouseMoved += Drag;
    }

    private void OnDisable()
    {
        MouseInputHandler.OnMouseClickDown -= Clicked;
        MouseInputHandler.OnMouseClickUp -= Released;
        MouseInputHandler.OnMouseMoved -= Drag;
    }

    private void Clicked(GameObject hitObject)
    {
        if (hitObject == null) return;
        if (hitObject.TryGetComponent<Item>(out Item dragItem))
        {
            isDragging = true;
            draggedItem = dragItem.transform as RectTransform;
            mouseStartPosition = Mouse.current.position.ReadValue();
            dragItemStartPosition = draggedItem.position;
        }
    }
    private void Released(GameObject hitObject)
    {
        if (draggedItem == null) return;

        List<InventorySlot> slots = FindObjectsByType<InventorySlot>(FindObjectsSortMode.None).ToList();
        //List<InventoryWhiteboard> whiteboards = FindObjectsByType<InventoryWhiteboard>(FindObjectsSortMode.None).ToList();

        // Find all slots that intersect with the dragged item
        List<InventorySlot> intersectingSlots = new();
        foreach (InventorySlot slot in slots)
        {
            RectTransform slotRect = slot.transform as RectTransform;
            if (DoRectTransformsIntersect(draggedItem, slotRect))
            {
                intersectingSlots.Add(slot);
            }
        }

        // Find the best fitting slot for the dragged item
        if (draggedItem.TryGetComponent(out Item draggedItemComponent))
        {
            InventorySlot bestSlot = FindBestFittingSlot(intersectingSlots, slots, draggedItemComponent);
            if (bestSlot != null)
            {
                // Get all slots that the item would occupy
                List<InventorySlot> occupiedSlots = GetSlotsForItem(bestSlot, draggedItemComponent, slots);

                Debug.Log($"Best fitting slot found at ({bestSlot.X}, {bestSlot.Y}) for item size ({draggedItemComponent.Size.x}, {draggedItemComponent.Size.y})");
                Debug.Log($"Item would occupy {occupiedSlots.Count} slots:");
                foreach (InventorySlot slot in occupiedSlots)
                {
                    Debug.Log($"  - Slot at ({slot.X}, {slot.Y})");
                }
            }
            else
            {
                Debug.Log($"No valid placement found for item size ({draggedItemComponent.Size.x}, {draggedItemComponent.Size.y})");
            }
        }

        // Debug output to show which slots are intersecting
        Debug.Log($"Dragged item intersects with {intersectingSlots.Count} slots:");
        foreach (InventorySlot slot in intersectingSlots)
        {
            Debug.Log($"  - Slot at ({slot.X}, {slot.Y})");
        }

        isDragging = false;
        draggedItem = null;
        mouseStartPosition = Vector2.zero;
        dragItemStartPosition = Vector2.zero;
    }
    private void Drag(Vector2 position)
    {
        if (!isDragging) return;
        Vector2 mousePosition = Mouse.current.position.ReadValue();
        draggedItem.position = dragItemStartPosition + (mousePosition - mouseStartPosition);
    }

    /// <summary>
    /// Checks if two RectTransforms intersect in any way (overlapping, touching, one inside the other)
    /// </summary>
    /// <param name="rect1">First RectTransform</param>
    /// <param name="rect2">Second RectTransform</param>
    /// <returns>True if the RectTransforms intersect, false otherwise</returns>
    private bool DoRectTransformsIntersect(RectTransform rect1, RectTransform rect2)
    {
        if (rect1 == null || rect2 == null) return false;

        // Get world space corners for both RectTransforms
        Vector3[] corners1 = new Vector3[4];
        Vector3[] corners2 = new Vector3[4];

        rect1.GetWorldCorners(corners1);
        rect2.GetWorldCorners(corners2);

        // Convert to screen space bounds for easier calculation
        // corners are in order: bottom-left, top-left, top-right, bottom-right
        float rect1MinX = corners1[0].x;
        float rect1MaxX = corners1[2].x;
        float rect1MinY = corners1[0].y;
        float rect1MaxY = corners1[1].y;

        float rect2MinX = corners2[0].x;
        float rect2MaxX = corners2[2].x;
        float rect2MinY = corners2[0].y;
        float rect2MaxY = corners2[1].y;

        // Check for intersection using AABB (Axis-Aligned Bounding Box) collision detection
        // Two rectangles intersect if they overlap on both X and Y axes
        bool xOverlap = rect1MinX <= rect2MaxX && rect1MaxX >= rect2MinX;
        bool yOverlap = rect1MinY <= rect2MaxY && rect1MaxY >= rect2MinY;

        return xOverlap && yOverlap;
    }

    /// <summary>
    /// Finds the best fitting slot for an item of any size
    /// </summary>
    /// <param name="intersectingSlots">Slots that intersect with the dragged item</param>
    /// <param name="allSlots">All available slots in the inventory</param>
    /// <param name="item">The item being placed</param>
    /// <returns>The best slot to place the item, or null if no valid placement exists</returns>
    private InventorySlot FindBestFittingSlot(List<InventorySlot> intersectingSlots, List<InventorySlot> allSlots, Item item)
    {
        if (intersectingSlots.Count == 0) return null;

        Vector2 itemSize = item.Size;
        int itemWidth = Mathf.RoundToInt(itemSize.x);
        int itemHeight = Mathf.RoundToInt(itemSize.y);

        // Create a dictionary for quick slot lookup by coordinates
        Dictionary<Vector2Int, InventorySlot> slotLookup = new();
        foreach (InventorySlot slot in allSlots)
        {
            slotLookup[new Vector2Int(slot.X, slot.Y)] = slot;
        }

        // Find valid top-left corner candidates from intersecting slots
        List<InventorySlot> validCandidates = new();

        foreach (InventorySlot intersectingSlot in intersectingSlots)
        {
            // Check if this slot can serve as a top-left corner for the item
            if (CanPlaceItemAtSlot(intersectingSlot, itemWidth, itemHeight, slotLookup))
            {
                validCandidates.Add(intersectingSlot);
            }
        }

        if (validCandidates.Count == 0) return null;

        // Find the best candidate based on distance to the item's center
        Vector3 itemCenter = draggedItem.position;
        InventorySlot bestSlot = null;
        float bestDistance = float.MaxValue;

        foreach (InventorySlot candidate in validCandidates)
        {
            Vector3 slotPosition = candidate.transform.position;
            float distance = Vector3.Distance(itemCenter, slotPosition);

            if (distance < bestDistance)
            {
                bestDistance = distance;
                bestSlot = candidate;
            }
        }

        return bestSlot;
    }

    /// <summary>
    /// Checks if an item can be placed at a specific slot (using the slot as top-left corner)
    /// </summary>
    /// <param name="topLeftSlot">The slot to use as top-left corner</param>
    /// <param name="itemWidth">Width of the item in grid cells</param>
    /// <param name="itemHeight">Height of the item in grid cells</param>
    /// <param name="slotLookup">Dictionary for quick slot lookup by coordinates</param>
    /// <returns>True if the item can be placed, false otherwise</returns>
    private bool CanPlaceItemAtSlot(InventorySlot topLeftSlot, int itemWidth, int itemHeight, Dictionary<Vector2Int, InventorySlot> slotLookup)
    {
        int startX = topLeftSlot.X;
        int startY = topLeftSlot.Y;

        // Check if all required slots exist and are available
        for (int x = startX; x < startX + itemWidth; x++)
        {
            for (int y = startY; y < startY + itemHeight; y++)
            {
                Vector2Int slotCoord = new(x, y);

                // Check if slot exists
                if (!slotLookup.ContainsKey(slotCoord))
                {
                    return false; // Slot doesn't exist (out of bounds)
                }

                InventorySlot slot = slotLookup[slotCoord];

                // Check if slot is occupied (assuming null means empty)
                if (slot.Item != null)
                {
                    return false; // Slot is occupied
                }
            }
        }

        return true; // All required slots are available
    }

    /// <summary>
    /// Gets all slots that an item would occupy when placed at a specific top-left slot
    /// </summary>
    /// <param name="topLeftSlot">The top-left corner slot where the item would be placed</param>
    /// <param name="item">The item to place</param>
    /// <param name="allSlots">All available slots in the inventory</param>
    /// <returns>List of all slots the item would occupy</returns>
    private List<InventorySlot> GetSlotsForItem(InventorySlot topLeftSlot, Item item, List<InventorySlot> allSlots)
    {
        List<InventorySlot> occupiedSlots = new();

        Vector2 itemSize = item.Size;
        int itemWidth = Mathf.RoundToInt(itemSize.x);
        int itemHeight = Mathf.RoundToInt(itemSize.y);

        // Create a dictionary for quick slot lookup by coordinates
        Dictionary<Vector2Int, InventorySlot> slotLookup = new();
        foreach (InventorySlot slot in allSlots)
        {
            slotLookup[new Vector2Int(slot.X, slot.Y)] = slot;
        }

        int startX = topLeftSlot.X;
        int startY = topLeftSlot.Y;

        // Collect all slots that the item would occupy
        for (int x = startX; x < startX + itemWidth; x++)
        {
            for (int y = startY; y < startY + itemHeight; y++)
            {
                Vector2Int slotCoord = new(x, y);

                if (slotLookup.ContainsKey(slotCoord))
                {
                    occupiedSlots.Add(slotLookup[slotCoord]);
                }
            }
        }

        return occupiedSlots;
    }

}